import React from 'react';
import PropTypes from 'prop-types';
import { getPageDimensions } from '../../models/ReportTypes';
import { COMPONENT_TYPES } from '../../models/ComponentTypes';
import { useReport } from '../../hooks/useReport';
import TextComponent from './TextComponent';
import ImageComponent from './ImageComponent';
import TableComponent from './TableComponent';
import './Page.css';

/**
 * Renders a single page in the report
 */
const Page = ({ page, zoom, isActive, onClick }) => {
  // Get dimensions based on page size and orientation
  const dimensions = getPageDimensions(page);

  // Get report context for component operations
  const { activeReport, updateComponent, selectComponent } = useReport();

  // Calculate the scale and dimensions for the page with zoom applied
  const pageStyle = {
    width: `${dimensions.width * (zoom / 100)}mm`,
    height: `${dimensions.height * (zoom / 100)}mm`,
    backgroundColor: page.backgroundColor || '#ffffff'
  };

  // Page wrapper style with zoom applied directly
  const pageWrapperStyle = {
    width: `${dimensions.width * (zoom / 100)}mm`,
    height: `${dimensions.height * (zoom / 100)}mm`
  };

  // Handle component selection
  const handleSelectComponent = (componentId) => {
    if (activeReport && isActive) {
      selectComponent(activeReport.id, page.id, componentId);
    }
  };

  // Handle component update
  const handleUpdateComponent = (componentId, updates) => {
    if (activeReport && isActive) {
      updateComponent(activeReport.id, page.id, componentId, updates);
    }
  };

  // Render a component based on its type
  const renderComponent = (component) => {
    const isSelected = page.selectedComponentId === component.id;

    switch (component.type) {
      case COMPONENT_TYPES.TEXT:
        return (
          <TextComponent
            key={component.id}
            component={component}
            zoom={zoom}
            isSelected={isSelected}
            pageMargins={page.margins}
            pageDimensions={dimensions}
            onSelect={handleSelectComponent}
            onUpdate={(updates) => handleUpdateComponent(component.id, updates)}
          />
        );
      case COMPONENT_TYPES.IMAGE:
        return (
          <ImageComponent
            key={component.id}
            component={component}
            zoom={zoom}
            isSelected={isSelected}
            pageMargins={page.margins}
            pageDimensions={dimensions}
            onSelect={handleSelectComponent}
            onUpdate={(updates) => handleUpdateComponent(component.id, updates)}
          />
        );
      case COMPONENT_TYPES.TABLE:
        return (
          <TableComponent
            key={component.id}
            component={component}
            zoom={zoom}
            isSelected={isSelected}
            pageMargins={page.margins}
            pageDimensions={dimensions}
            onSelect={handleSelectComponent}
            onUpdate={(updates) => handleUpdateComponent(component.id, updates)}
          />
        );
      default:
        return null;
    }
  };

  // Handle page click (deselect components)
  const handlePageClick = (e) => {
    // Only handle if clicking directly on the page (not on a component)
    if (e.target === e.currentTarget) {
      // Deselect components if this is the active page
      if (activeReport && isActive) {
        selectComponent(activeReport.id, page.id, null);
      }

      // Only call the parent onClick handler for actual page clicks
      // This prevents zoom resets when clicking on components
      onClick();
    }
    // If clicking on a component, don't call onClick() to avoid zoom reset
  };

  return (
    <div
      id={`page-${page.id}`}
      className={`page-wrapper ${isActive ? 'active' : ''}`}
      style={pageWrapperStyle}
    >
      <div
        className={`page ${isActive ? 'active' : ''}`}
        style={pageStyle}
        onClick={handlePageClick}
      >
        <div
          className="page-content"
          style={{
            padding: `${page.margins.top * (zoom / 100)}mm ${page.margins.right * (zoom / 100)}mm ${page.margins.bottom * (zoom / 100)}mm ${page.margins.left * (zoom / 100)}mm`,
            position: 'relative', /* Ensure this is a positioning context for absolute children */
            margin: 0,
            boxSizing: 'border-box'
          }}
        >
          {/* Render page components */}
          {page.components && page.components.length > 0 ? (
            page.components.map(renderComponent)
          ) : (
            <div className="empty-page-message">
              {isActive && <span>Click to add content</span>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

Page.propTypes = {
  page: PropTypes.object.isRequired,
  zoom: PropTypes.number.isRequired,
  isActive: PropTypes.bool,
  onClick: PropTypes.func
};

Page.defaultProps = {
  isActive: false,
  onClick: () => {}
};

export default Page;
